package com.nacos.demo;

import com.nacos.utils.AliTranslateUtil;
import com.nacos.enums.AliTranslateLanguageEnum;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.List;

/**
 * 阿里云机器翻译功能演示
 * 
 * <p>演示如何使用新集成的阿里云机器翻译API进行文本翻译。
 * 包括单文本翻译、批量翻译、字幕翻译等场景。</p>
 * 
 * <h3>使用说明</h3>
 * <ul>
 *   <li>确保阿里云API密钥配置正确</li>
 *   <li>确保网络连接正常</li>
 *   <li>注意API调用频率限制（QPS 50）</li>
 * </ul>
 * 
 * <AUTHOR>
 * @since 2025-08-05
 */
@Slf4j
public class TranslationDemo {

    public static void main(String[] args) {
        TranslationDemo demo = new TranslationDemo();
        
        System.out.println("=== 阿里云机器翻译功能演示 ===\n");
        
        // 1. 单文本翻译演示
        demo.demonstrateSingleTextTranslation();
        
        // 2. 批量翻译演示
        demo.demonstrateBatchTranslation();
        
        // 3. 字幕翻译演示
        demo.demonstrateSubtitleTranslation();
        
        // 4. 错误处理演示
        demo.demonstrateErrorHandling();

        // 5. 语言代码验证演示
        demo.demonstrateLanguageCodeValidation();

        System.out.println("\n=== 演示完成 ===");
    }

    /**
     * 演示单文本翻译
     */
    public void demonstrateSingleTextTranslation() {
        System.out.println("1. 单文本翻译演示");
        System.out.println("==================");
        
        // 中文到英文
        translateAndPrint("你好，世界！这是一个机器翻译的演示。", "zh", "en");
        
        // 英文到中文
        translateAndPrint("Hello, world! This is a machine translation demo.", "en", "zh");
        
        // 中文到日文
        translateAndPrint("今天天气很好。", "zh", "ja");
        
        System.out.println();
    }

    /**
     * 演示批量翻译
     */
    public void demonstrateBatchTranslation() {
        System.out.println("2. 批量翻译演示");
        System.out.println("================");
        
        List<String> chineseTexts = Arrays.asList(
                "你好",
                "再见", 
                "谢谢",
                "不客气",
                "请问",
                "对不起"
        );
        
        System.out.println("批量翻译中文常用语到英文：");
        AliTranslateUtil.BatchTranslationResult result = AliTranslateUtil.translateBatch(
                chineseTexts, "zh", "en", "general");
        
        if (result.isSuccess()) {
            List<String> translatedTexts = result.getTranslatedTexts();
            for (int i = 0; i < chineseTexts.size(); i++) {
                System.out.printf("  %s -> %s%n", chineseTexts.get(i), translatedTexts.get(i));
            }
            System.out.printf("成功翻译: %d/%d%n", result.getSuccessCount(), chineseTexts.size());
        } else {
            System.out.println("批量翻译失败: " + result.getErrorMessage());
        }
        
        System.out.println();
    }

    /**
     * 演示字幕翻译
     */
    public void demonstrateSubtitleTranslation() {
        System.out.println("3. 字幕翻译演示");
        System.out.println("================");
        
        String subtitleContent = "大家好，欢迎观看这个视频教程。" +
                "今天我们将学习如何使用阿里云机器翻译API。" +
                "这个API支持多种语言之间的翻译，" +
                "包括中文、英文、日文、韩文等。" +
                "希望这个教程对大家有帮助。";
        
        System.out.println("原始字幕内容:");
        System.out.println(subtitleContent);
        System.out.println();
        
        AliTranslateUtil.TranslationResult result = AliTranslateUtil.translateText(
                subtitleContent, "zh", "en", "general");
        
        if (result.isSuccess()) {
            System.out.println("翻译后的字幕内容:");
            System.out.println(result.getTranslatedText());
            System.out.printf("翻译统计: 词数=%d, 检测语言=%s%n", 
                    result.getWordCount(), result.getDetectedLanguage());
        } else {
            System.out.println("字幕翻译失败: " + result.getErrorMessage());
        }
        
        System.out.println();
    }

    /**
     * 演示错误处理
     */
    public void demonstrateErrorHandling() {
        System.out.println("4. 错误处理演示");
        System.out.println("================");
        
        // 空文本测试
        System.out.println("测试空文本翻译:");
        AliTranslateUtil.TranslationResult emptyResult = AliTranslateUtil.translateText(
                "", "zh", "en", "general");
        System.out.println("结果: " + (emptyResult.isSuccess() ? "成功" : "失败 - " + emptyResult.getErrorMessage()));
        
        // 相同语言测试
        System.out.println("\n测试相同语言翻译:");
        AliTranslateUtil.TranslationResult sameResult = AliTranslateUtil.translateText(
                "Hello, world!", "en", "en", "general");
        System.out.println("结果: " + (sameResult.isSuccess() ? "成功 - " + sameResult.getTranslatedText() : "失败"));
        
        // 超长文本测试
        System.out.println("\n测试超长文本翻译:");
        StringBuilder longText = new StringBuilder();
        for (int i = 0; i < 5001; i++) {
            longText.append("a");
        }
        AliTranslateUtil.TranslationResult longResult = AliTranslateUtil.translateText(
                longText.toString(), "en", "zh", "general");
        System.out.println("结果: " + (longResult.isSuccess() ? "成功" : "失败 - " + longResult.getErrorMessage()));
        
        System.out.println();
    }

    /**
     * 翻译并打印结果的辅助方法
     */
    private void translateAndPrint(String text, String sourceLanguage, String targetLanguage) {
        System.out.printf("翻译: %s (%s -> %s)%n", text, sourceLanguage, targetLanguage);
        
        AliTranslateUtil.TranslationResult result = AliTranslateUtil.translateText(
                text, sourceLanguage, targetLanguage, "general");
        
        if (result.isSuccess()) {
            System.out.printf("结果: %s%n", result.getTranslatedText());
            System.out.printf("统计: 词数=%d, 检测语言=%s%n", 
                    result.getWordCount(), result.getDetectedLanguage());
        } else {
            System.out.printf("翻译失败: %s%n", result.getErrorMessage());
        }
        System.out.println();
    }

    /**
     * 演示视频翻译处理器中的使用方式
     */
    public void demonstrateVideoTranslationUsage() {
        System.out.println("5. 视频翻译处理器使用演示");
        System.out.println("==========================");
        
        // 模拟从视频中提取的字幕内容
        String extractedSubtitle = "WEBVTT\n\n" +
                "00:00:01.000 --> 00:00:05.000\n" +
                "欢迎来到我们的在线课程。\n\n" +
                "00:00:05.000 --> 00:00:10.000\n" +
                "今天我们将学习人工智能的基础知识。\n\n" +
                "00:00:10.000 --> 00:00:15.000\n" +
                "请大家认真听讲，积极参与讨论。";
        
        System.out.println("提取的字幕内容:");
        System.out.println(extractedSubtitle);
        System.out.println();
        
        // 模拟视频翻译处理器的调用方式
        String sourceLanguage = "zh";
        String targetLanguage = "en";
        
        System.out.printf("执行翻译: %s -> %s%n", sourceLanguage, targetLanguage);
        
        AliTranslateUtil.TranslationResult result = AliTranslateUtil.translateText(
                extractedSubtitle, sourceLanguage, targetLanguage, "general");
        
        if (result.isSuccess()) {
            System.out.println("翻译成功！");
            System.out.println("翻译后的字幕:");
            System.out.println(result.getTranslatedText());
            
            // 这里可以继续进行语音合成等后续步骤
            System.out.println("\n后续步骤:");
            System.out.println("1. 将翻译后的文本发送给语音合成服务");
            System.out.println("2. 生成对应的音频文件");
            System.out.println("3. 将新音频与原视频合成");
            System.out.println("4. 返回最终的翻译视频");
        } else {
            System.out.println("翻译失败: " + result.getErrorMessage());
        }

        System.out.println();
    }

    /**
     * 演示语言代码验证功能
     */
    public void demonstrateLanguageCodeValidation() {
        System.out.println("5. 语言代码验证演示");
        System.out.println("====================");

        // 演示支持的语言代码验证
        System.out.println("支持的语言代码验证:");
        String[] testCodes = {"zh", "en", "ja", "ko", "fr", "de", "es", "invalid", "xyz", ""};
        for (String code : testCodes) {
            boolean isSupported = AliTranslateUtil.isSupportedLanguage(code);
            String languageName = AliTranslateUtil.getLanguageName(code);
            System.out.printf("  %s: %s %s%n",
                    code.isEmpty() ? "(空)" : code,
                    isSupported ? "✓支持" : "✗不支持",
                    languageName != null ? "(" + languageName + ")" : "");
        }

        System.out.println();

        // 演示语言对互译验证
        System.out.println("语言对互译验证:");
        String[][] languagePairs = {
                {"zh", "en", "中文 ↔ 英文"},
                {"en", "ja", "英文 ↔ 日文"},
                {"zh", "zh-tw", "中文 ↔ 繁体中文"},
                {"zh-tw", "en", "繁体中文 ↔ 英文"},
                {"yue", "zh", "粤语 ↔ 中文"},
                {"yue", "en", "粤语 ↔ 英文"},
                {"zh-tw", "yue", "繁体中文 ↔ 粤语"},
                {"invalid", "en", "无效语言 ↔ 英文"}
        };

        for (String[] pair : languagePairs) {
            boolean canTranslate = AliTranslateUtil.isSupportedLanguagePair(pair[0], pair[1]);
            System.out.printf("  %s: %s%n", pair[2], canTranslate ? "✓支持" : "✗不支持");
        }

        System.out.println();

        // 演示获取支持的语言列表
        System.out.println("支持的语言统计:");
        String[] allCodes = AliTranslateUtil.getSupportedLanguageCodes();
        String[] commonCodes = AliTranslateUtil.getCommonLanguageCodes();
        System.out.printf("  总支持语言数: %d种%n", allCodes.length);
        System.out.printf("  常用语言数: %d种%n", commonCodes.length);

        System.out.println();
        System.out.println("常用语言列表:");
        for (String code : commonCodes) {
            String name = AliTranslateUtil.getLanguageName(code);
            System.out.printf("  %s: %s%n", code, name);
        }

        System.out.println();

        // 演示特殊语言的限制
        System.out.println("特殊语言限制说明:");
        System.out.println("  • 繁体中文(zh-tw)、蒙语(mn)、粤语(yue) 仅支持与中文(zh)互译");
        System.out.println("  • 其他212种语言支持任意两种语言之间互译");
        System.out.println("  • 支持自动语言检测(auto)，但粤语为源语言时不支持auto");

        System.out.println();

        // 演示实际的语言代码验证错误
        System.out.println("语言代码验证错误演示:");

        // 无效语言代码
        AliTranslateUtil.TranslationResult result1 = AliTranslateUtil.translateText(
                "Hello", "invalid", "zh", "general");
        System.out.println("  无效源语言代码: " + result1.getErrorMessage());

        // 不支持的语言对
        AliTranslateUtil.TranslationResult result2 = AliTranslateUtil.translateText(
                "你好", "zh-tw", "en", "general");
        System.out.println("  不支持的语言对: " + result2.getErrorMessage());

        System.out.println();
    }
}
