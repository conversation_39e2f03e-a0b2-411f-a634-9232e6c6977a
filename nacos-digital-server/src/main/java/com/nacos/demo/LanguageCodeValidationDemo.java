package com.nacos.demo;

import com.nacos.enums.AliTranslateLanguageEnum;
import com.nacos.utils.AliTranslateUtil;

/**
 * 语言代码验证功能演示
 * 
 * <p>演示阿里云机器翻译语言代码验证功能，包括：</p>
 * <ul>
 *   <li>语言代码有效性验证</li>
 *   <li>语言对互译支持验证</li>
 *   <li>特殊语言限制处理</li>
 *   <li>错误信息展示</li>
 * </ul>
 * 
 * <AUTHOR>
 * @since 2025-08-05
 */
public class LanguageCodeValidationDemo {

    public static void main(String[] args) {
        LanguageCodeValidationDemo demo = new LanguageCodeValidationDemo();
        
        System.out.println("=== 阿里云机器翻译语言代码验证演示 ===\n");
        
        demo.demonstrateLanguageCodeValidation();
        demo.demonstrateLanguagePairValidation();
        demo.demonstrateSpecialLanguageRestrictions();
        demo.demonstrateErrorHandling();
        demo.demonstrateLanguageInformation();
        
        System.out.println("=== 演示完成 ===");
    }

    /**
     * 演示语言代码验证
     */
    public void demonstrateLanguageCodeValidation() {
        System.out.println("1. 语言代码验证演示");
        System.out.println("==================");
        
        String[] testCodes = {
                "zh", "en", "ja", "ko", "fr", "de", "es", "pt", "ru", "ar",
                "zh-tw", "yue", "mn", "auto",
                "invalid", "xyz", "", "zh-cn", "EN"
        };
        
        System.out.println("验证各种语言代码:");
        for (String code : testCodes) {
            boolean isSupported = AliTranslateLanguageEnum.isSupported(code);
            String languageName = AliTranslateUtil.getLanguageName(code);
            
            String status = isSupported ? "✓ 支持" : "✗ 不支持";
            String name = languageName != null ? " (" + languageName + ")" : "";
            String displayCode = code.isEmpty() ? "(空字符串)" : code;
            
            System.out.printf("  %-10s: %s%s%n", displayCode, status, name);
        }
        
        System.out.println();
    }

    /**
     * 演示语言对互译验证
     */
    public void demonstrateLanguagePairValidation() {
        System.out.println("2. 语言对互译验证演示");
        System.out.println("====================");
        
        String[][] languagePairs = {
                // 常规语言对（支持）
                {"zh", "en", "中文 ↔ 英文"},
                {"en", "ja", "英文 ↔ 日文"},
                {"fr", "de", "法文 ↔ 德文"},
                {"ko", "es", "韩文 ↔ 西班牙文"},
                
                // 特殊语言与中文（支持）
                {"zh", "zh-tw", "中文 ↔ 繁体中文"},
                {"zh-tw", "zh", "繁体中文 ↔ 中文"},
                {"zh", "yue", "中文 ↔ 粤语"},
                {"yue", "zh", "粤语 ↔ 中文"},
                {"zh", "mn", "中文 ↔ 蒙古语"},
                {"mn", "zh", "蒙古语 ↔ 中文"},
                
                // 特殊语言之间（不支持）
                {"zh-tw", "yue", "繁体中文 ↔ 粤语"},
                {"yue", "zh-tw", "粤语 ↔ 繁体中文"},
                {"zh-tw", "mn", "繁体中文 ↔ 蒙古语"},
                {"mn", "yue", "蒙古语 ↔ 粤语"},
                
                // 特殊语言与其他语言（不支持）
                {"zh-tw", "en", "繁体中文 ↔ 英文"},
                {"en", "zh-tw", "英文 ↔ 繁体中文"},
                {"yue", "ja", "粤语 ↔ 日文"},
                {"ja", "yue", "日文 ↔ 粤语"},
                
                // 相同语言（支持）
                {"zh", "zh", "中文 ↔ 中文"},
                {"en", "en", "英文 ↔ 英文"},
                
                // 无效语言代码（不支持）
                {"invalid", "en", "无效语言 ↔ 英文"},
                {"zh", "invalid", "中文 ↔ 无效语言"}
        };
        
        System.out.println("验证各种语言对互译支持:");
        for (String[] pair : languagePairs) {
            boolean canTranslate = AliTranslateLanguageEnum.canTranslate(pair[0], pair[1]);
            String status = canTranslate ? "✓ 支持" : "✗ 不支持";
            System.out.printf("  %-25s: %s%n", pair[2], status);
        }
        
        System.out.println();
    }

    /**
     * 演示特殊语言限制
     */
    public void demonstrateSpecialLanguageRestrictions() {
        System.out.println("3. 特殊语言限制演示");
        System.out.println("==================");
        
        System.out.println("阿里云机器翻译特殊语言限制说明:");
        System.out.println("• 繁体中文(zh-tw)：仅支持与中文(zh)互译");
        System.out.println("• 蒙古语(mn)：仅支持与中文(zh)互译");
        System.out.println("• 粤语(yue)：仅支持与中文(zh)互译");
        System.out.println("• 其他212种语言：支持任意两种语言之间互译");
        System.out.println("• 自动检测(auto)：支持源语言自动检测，但粤语为源语言时不支持");
        
        System.out.println();
        
        // 验证特殊语言的限制
        String[] restrictedLanguages = {"zh-tw", "mn", "yue"};
        String[] otherLanguages = {"en", "ja", "ko", "fr", "de"};
        
        System.out.println("验证特殊语言限制:");
        for (String restricted : restrictedLanguages) {
            String restrictedName = AliTranslateUtil.getLanguageName(restricted);
            System.out.printf("%s(%s) 的翻译支持情况:%n", restrictedName, restricted);
            
            // 与中文的互译（应该支持）
            boolean toZh = AliTranslateLanguageEnum.canTranslate(restricted, "zh");
            boolean fromZh = AliTranslateLanguageEnum.canTranslate("zh", restricted);
            System.out.printf("  → 中文: %s%n", toZh ? "✓ 支持" : "✗ 不支持");
            System.out.printf("  ← 中文: %s%n", fromZh ? "✓ 支持" : "✗ 不支持");
            
            // 与其他语言的互译（应该不支持）
            for (String other : otherLanguages) {
                String otherName = AliTranslateUtil.getLanguageName(other);
                boolean canTranslate = AliTranslateLanguageEnum.canTranslate(restricted, other);
                System.out.printf("  → %s(%s): %s%n", otherName, other, 
                        canTranslate ? "✓ 支持" : "✗ 不支持");
                break; // 只显示一个例子
            }
            System.out.println();
        }
    }

    /**
     * 演示错误处理
     */
    public void demonstrateErrorHandling() {
        System.out.println("4. 错误处理演示");
        System.out.println("================");
        
        System.out.println("演示各种错误情况的处理:");
        
        // 无效源语言代码
        System.out.println("1. 无效源语言代码:");
        AliTranslateUtil.TranslationResult result1 = AliTranslateUtil.translateText(
                "Hello", "invalid", "zh", "general");
        System.out.printf("   错误信息: %s%n", result1.getErrorMessage());
        
        // 无效目标语言代码
        System.out.println("2. 无效目标语言代码:");
        AliTranslateUtil.TranslationResult result2 = AliTranslateUtil.translateText(
                "Hello", "en", "invalid", "general");
        System.out.printf("   错误信息: %s%n", result2.getErrorMessage());
        
        // 不支持的语言对
        System.out.println("3. 不支持的语言对:");
        AliTranslateUtil.TranslationResult result3 = AliTranslateUtil.translateText(
                "你好", "zh-tw", "en", "general");
        System.out.printf("   错误信息: %s%n", result3.getErrorMessage());
        
        // 空语言代码
        System.out.println("4. 空语言代码:");
        AliTranslateUtil.TranslationResult result4 = AliTranslateUtil.translateText(
                "Hello", "", "zh", "general");
        System.out.printf("   错误信息: %s%n", result4.getErrorMessage());
        
        System.out.println();
    }

    /**
     * 演示语言信息获取
     */
    public void demonstrateLanguageInformation() {
        System.out.println("5. 语言信息获取演示");
        System.out.println("==================");
        
        // 获取支持的语言统计
        String[] allCodes = AliTranslateUtil.getSupportedLanguageCodes();
        String[] commonCodes = AliTranslateUtil.getCommonLanguageCodes();
        
        System.out.printf("支持的语言总数: %d种%n", allCodes.length);
        System.out.printf("常用语言数量: %d种%n", commonCodes.length);
        System.out.println();
        
        // 显示常用语言列表
        System.out.println("常用语言列表:");
        for (String code : commonCodes) {
            String name = AliTranslateUtil.getLanguageName(code);
            AliTranslateLanguageEnum language = AliTranslateLanguageEnum.getByCode(code);
            String englishName = language != null ? language.getEnglishName() : "Unknown";
            System.out.printf("  %s: %s (%s)%n", code, name, englishName);
        }
        
        System.out.println();
        
        // 演示枚举类的详细信息
        System.out.println("语言枚举详细信息示例:");
        String[] exampleCodes = {"zh", "en", "ja", "zh-tw", "yue", "auto"};
        for (String code : exampleCodes) {
            AliTranslateLanguageEnum language = AliTranslateLanguageEnum.getByCode(code);
            if (language != null) {
                System.out.printf("  %s: %s | %s | %s%n", 
                        language.getCode(),
                        language.getChineseName(),
                        language.getEnglishName(),
                        language.toString());
            }
        }
        
        System.out.println();
    }
}
