package com.nacos.service;

import com.nacos.entity.dto.VideoTranslateRequestDTO;
import com.nacos.entity.vo.VideoTranslateStatusVO;
import com.nacos.entity.vo.LanguageVO;
import com.nacos.entity.vo.VoiceVO;
import com.nacos.result.Result;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 视频翻译服务接口
 * 基于羚羊平台的视频翻译任务管理服务
 * 参考DigitalVideoService的设计模式
 * 
 * <AUTHOR>
 * @since 2025-01-29
 */
public interface VideoTranslateService {

    /**
     * 提交视频翻译任务
     * 支持两种模式：
     * 1. URL模式：通过request.videoUrl指定视频文件URL
     * 2. 文件上传模式：通过file参数直接上传视频文件
     *
     * @param request 翻译请求参数
     * @param file 视频文件（可选，如果提供则优先使用文件上传）
     * @return 任务提交结果，包含任务ID
     */
    Result<Map<String, Object>> submitTranslateTask(VideoTranslateRequestDTO request, MultipartFile file);

    /**
     * 查询任务状态
     * 
     * @param taskId 任务ID
     * @return 任务状态信息
     */
    Result<VideoTranslateStatusVO> getTaskStatus(String taskId);

    /**
     * 处理排队中的任务
     * 定时任务调用，处理状态为submitted的任务
     */
    void processQueueingTasks();

    /**
     * 处理超时任务
     * 定时任务调用，处理超时的任务
     */
    void processTimeoutTasks();

    /**
     * 同步任务状态
     * 定时任务调用，同步羚羊平台的任务状态
     */
    void syncTaskStatus();

    /**
     * 根据羚羊任务ID查询本地任务
     * 
     * @param lingyangTaskId 羚羊平台任务ID
     * @return 本地任务信息
     */
    Result<VideoTranslateStatusVO> getTaskByLingyangTaskId(String lingyangTaskId);

    /**
     * 更新任务状态
     * 内部方法，用于更新任务状态和进度
     * 
     * @param taskId 任务ID
     * @param status 新状态
     * @param progress 进度百分比
     * @param currentStep 当前步骤描述
     * @param errorMessage 错误信息（可选）
     * @return 更新结果
     */
    Result<Boolean> updateTaskStatus(String taskId, String status, Integer progress, 
                                   String currentStep, String errorMessage);

    /**
     * 完成任务处理
     * 当任务完成时调用，更新结果信息
     * 
     * @param taskId 任务ID
     * @param resultVideoUrl 结果视频URL
     * @param subtitleUrl 字幕文件URL
     * @param originalText 识别的原文本
     * @param translatedText 翻译后的文本
     * @return 更新结果
     */
    Result<Boolean> completeTask(String taskId, String resultVideoUrl, String subtitleUrl,
                               String originalText, String translatedText);

    /**
     * 标记任务失败
     * 当任务失败时调用
     * 
     * @param taskId 任务ID
     * @param errorMessage 错误信息
     * @return 更新结果
     */
    Result<Boolean> failTask(String taskId, String errorMessage);

    /**
     * 获取用户任务统计信息
     * 
     * @param userId 用户ID
     * @return 统计信息
     */
    Result<Map<String, Object>> getUserTaskStats(String userId);

    /**
     * 重试失败的任务
     *
     * @param taskId 任务ID
     * @param userId 用户ID
     * @return 重试结果
     */
    Result<String> retryTask(String taskId, String userId);

    // ==================== 任务历史查询功能 ====================

    /**
     * 分页查询任务历史
     * @param userId 用户ID（可选）
     * @param status 任务状态（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param page 页码（从1开始）
     * @param size 每页大小
     * @param sortBy 排序字段（createdTime, startTime, finishTime等）
     * @param sortOrder 排序方向（ASC, DESC）
     * @return 分页结果
     */
    Result<Map<String, Object>> queryTaskHistory(String userId, Integer status,
                                                 LocalDateTime startTime, LocalDateTime endTime,
                                                 Integer page, Integer size,
                                                 String sortBy, String sortOrder);

    /**
     * 查询视频翻译任务列表
     *
     * @param userId 用户ID
     * @param status 状态筛选（success,processing等）
     * @param page 页码
     * @param size 每页大小
     * @param sortBy 排序字段
     * @param sortOrder 排序方向
     * @return 分页任务列表
     */
    Result<Map<String, Object>> queryTaskList(String userId, String status,
                                              Integer page, Integer size,
                                              String sortBy, String sortOrder);

    /**
     * 获取用户任务历史统计
     * @param userId 用户ID
     * @param days 统计天数（默认30天）
     * @return 历史统计信息
     */
    Result<Map<String, Object>> getUserTaskHistoryStats(String userId, Integer days);

    /**
     * 导出任务历史数据
     * @param userId 用户ID（可选）
     * @param status 任务状态（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param format 导出格式（CSV, EXCEL）
     * @return 导出文件信息
     */
    Result<Map<String, Object>> exportTaskHistory(String userId, Integer status,
                                                  LocalDateTime startTime, LocalDateTime endTime,
                                                  String format);

    /**
     * 获取任务执行趋势数据
     * @param userId 用户ID（可选）
     * @param days 统计天数
     * @param granularity 时间粒度（HOUR, DAY, WEEK）
     * @return 趋势数据
     */
    Result<Map<String, Object>> getTaskTrendData(String userId, Integer days, String granularity);
}
