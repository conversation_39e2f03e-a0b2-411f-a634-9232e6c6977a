package com.nacos.enums;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 阿里云机器翻译支持的语言代码枚举
 * 
 * <p>基于阿里云官方文档：https://help.aliyun.com/zh/machine-translation/support/supported-languages-and-codes</p>
 * 
 * <h3>语言支持说明</h3>
 * <ul>
 *   <li><strong>文本翻译</strong>：除繁体中文、蒙语、粤语外，其他212种语言可支持任意两种语言之间互译</li>
 *   <li><strong>繁体中文、蒙语、粤语</strong>：仅支持与中文之间的互译</li>
 *   <li><strong>自动检测</strong>：支持源语言的自动语言检测，语言代码为auto（粤语为源语言时，不支持使用auto）</li>
 *   <li><strong>图片翻译</strong>：仅支持原图为中/英文的图片翻译成其他语言</li>
 * </ul>
 * 
 * <h3>使用示例</h3>
 * <pre>
 * // 根据语言代码查找枚举
 * AliTranslateLanguageEnum language = AliTranslateLanguageEnum.getByCode("zh");
 * if (language != null) {
 *     System.out.println(language.getChineseName()); // 输出：中文
 * }
 * 
 * // 验证语言代码是否支持
 * boolean isSupported = AliTranslateLanguageEnum.isSupported("en");
 * 
 * // 验证语言对是否支持互译
 * boolean canTranslate = AliTranslateLanguageEnum.canTranslate("zh", "en");
 * </pre>
 * 
 * <AUTHOR>
 * @since 2025-08-05
 * @version 1.0
 */
public enum AliTranslateLanguageEnum {

    // 自动检测
    AUTO("auto", "自动检测", "Auto Detect"),
    
    // A
    ABKHAZIAN("ab", "阿布哈兹语", "Abkhazian"),
    ALBANIAN("sq", "阿尔巴尼亚语", "Albanian"),
    AKAN("ak", "阿肯语", "Akan"),
    ARABIC("ar", "阿拉伯语", "Arabic"),
    ARAGONESE("an", "阿拉贡语", "Aragonese"),
    AMHARIC("am", "阿姆哈拉语", "Amharic"),
    ASSAMESE("as", "阿萨姆语", "Assamese"),
    AZERBAIJANI("az", "阿塞拜疆语", "Azerbaijani"),
    ASTURIAN("ast", "阿斯图里亚斯语", "Asturian"),
    CENTRAL_HUASTECA_NAHUATL("nch", "阿兹特克语", "Central Huasteca Nahuatl"),
    EWE("ee", "埃维语", "Ewe"),
    AYMARA("ay", "艾马拉语", "Aymara"),
    IRISH("ga", "爱尔兰语", "Irish"),
    ESTONIAN("et", "爱沙尼亚语", "Estonian"),
    OJIBWA("oj", "奥杰布瓦语", "Ojibwa"),
    OCCITAN("oc", "奥克语", "Occitan"),
    ORIYA("or", "奥里亚语", "Oriya"),
    OROMO("om", "奥罗莫语", "Oromo"),
    OSSETIAN("os", "奥塞梯语", "Ossetian"),
    
    // B
    TOK_PISIN("tpi", "巴布亚皮钦语", "Tok Pisin"),
    BASHKIR("ba", "巴什基尔语", "Bashkir"),
    BASQUE("eu", "巴斯克语", "Basque"),
    BELARUSIAN("be", "白俄罗斯语", "Belarusian"),
    BERBER_LANGUAGES("ber", "柏柏尔语", "Berber languages"),
    BAMBARA("bm", "班巴拉语", "Bambara"),
    PANGASINAN("pag", "邦阿西楠语", "Pangasinan"),
    BULGARIAN("bg", "保加利亚语", "Bulgarian"),
    NORTHERN_SAMI("se", "北萨米语", "Northern Sami"),
    BEMBA_ZAMBIA("bem", "本巴语", "Bemba (Zambia)"),
    BLIN("byn", "比林语", "Blin"),
    BISLAMA("bi", "比斯拉马语", "Bislama"),
    BALUCHI("bal", "俾路支语", "Baluchi"),
    ICELANDIC("is", "冰岛语", "Icelandic"),
    POLISH("pl", "波兰语", "Polish"),
    BOSNIAN("bs", "波斯尼亚语", "Bosnian"),
    PERSIAN("fa", "波斯语", "Persian"),
    BHOJPURI("bho", "博杰普尔语", "Bhojpuri"),
    BRETON("br", "布列塔尼语", "Breton"),
    
    // C
    CHAMORRO("ch", "查莫罗语", "Chamorro"),
    CHAVACANO("cbk", "查瓦卡诺语", "Chavacano"),
    CHUVASH("cv", "楚瓦什语", "Chuvash"),
    TSONGA("ts", "聪加语", "Tsonga"),
    
    // D
    TATAR("tt", "鞑靼语", "Tatar"),
    DANISH("da", "丹麦语", "Danish"),
    SHAN("shn", "掸语", "Shan"),
    TETUM("tet", "德顿语", "Tetum"),
    GERMAN("de", "德语", "German"),
    LOW_GERMAN("nds", "低地德语", "Low German"),
    SCOTS("sco", "低地苏格兰语", "Scots"),
    DHIVEHI("dv", "迪维西语", "Dhivehi"),
    KAM("kdx", "侗语", "Kam"),
    KADAZAN_DUSUN("dtp", "杜順語", "Kadazan Dusun"),
    
    // E
    RUSSIAN("ru", "俄语", "Russian"),
    
    // F
    FAROESE("fo", "法罗语", "Faroese"),
    FRENCH("fr", "法语", "French"),
    SANSKRIT("sa", "梵语", "Sanskrit"),
    FILIPINO("fil", "菲律宾语", "Filipino"),
    FIJIAN("fj", "斐济语", "Fijian"),
    FINNISH("fi", "芬兰语", "Finnish"),
    FRIULIAN("fur", "弗留利语", "Friulian"),
    FUR("fvr", "富尔语", "Fur"),
    
    // G
    KONGO("kg", "刚果语", "Kongo"),
    KHMER("km", "高棉语", "Khmer"),
    GUERRERO_NAHUATL("ngu", "格雷罗纳瓦特尔语", "Guerrero Nahuatl"),
    KALAALLISUT("kl", "格陵兰语", "Kalaallisut"),
    GEORGIAN("ka", "格鲁吉亚语", "Georgian"),
    GRONINGS("gos", "格罗宁根方言", "Gronings"),
    GUJARATI("gu", "古吉拉特语", "Gujarati"),
    GUARANI("gn", "瓜拉尼语", "Guarani"),
    
    // H
    KAZAKH("kk", "哈萨克语", "Kazakh"),
    HAITIAN("ht", "海地克里奥尔语", "Haitian"),
    KOREAN("ko", "韩语", "Korean"),
    HAUSA("ha", "豪萨语", "Hausa"),
    DUTCH("nl", "荷兰语", "Dutch"),
    MONTENEGRIN("cnr", "黑山语", "Montenegrin"),
    HUPA("hup", "胡帕语", "Hupa"),
    
    // J
    GILBERTESE("gil", "基里巴斯语", "Gilbertese"),
    RUNDI("rn", "基隆迪语", "Rundi"),
    KICHE("quc", "基切语", "K'iche'"),
    KIRGHIZ("ky", "吉尔吉斯斯坦语", "Kirghiz"),
    GALICIAN("gl", "加利西亚语", "Galician"),
    CATALAN("ca", "加泰罗尼亚语", "Catalan"),
    CZECH("cs", "捷克语", "Czech"),
    
    // K
    KABYLE("kab", "卡拜尔语", "Kabyle"),
    KANNADA("kn", "卡纳达语", "Kannada"),
    KANURI("kr", "卡努里语", "Kanuri"),
    KASHUBIAN("csb", "卡舒比语", "Kashubian"),
    KHASI("kha", "卡西语", "Khasi"),
    CORNISH("kw", "康沃尔语", "Cornish"),
    XHOSA("xh", "科萨语", "Xhosa"),
    CORSICAN("co", "科西嘉语", "Corsican"),
    CREEK("mus", "克里克语", "Creek"),
    CRIMEAN_TATAR("crh", "克里米亚鞑靼语", "Crimean Tatar"),
    KLINGON("tlh", "克林贡语", "Klingon"),
    SERBO_CROATIAN("hbs", "克罗地亚语", "Serbo-Croatian"),
    QUECHUA("qu", "克丘亚语", "Quechua"),
    KASHMIRI("ks", "克什米尔语", "Kashmiri"),
    KURDISH("ku", "库尔德语", "Kurdish"),
    
    // L
    LATIN("la", "拉丁语", "Latin"),
    LATGALIAN("ltg", "拉特加莱语", "Latgalian"),
    LATVIAN("lv", "拉脱维亚语", "Latvian"),
    LAO("lo", "老挝语", "Lao"),
    LITHUANIAN("lt", "立陶宛语", "Lithuanian"),
    LIMBURGISH("li", "林堡语", "Limburgish"),
    LINGALA("ln", "林加拉语", "Lingala"),
    GANDA("lg", "卢干达语", "Ganda"),
    LETZEBURGESCH("lb", "卢森堡语", "Letzeburgesch"),
    RUSYN("rue", "卢森尼亚语", "Rusyn"),
    KINYARWANDA("rw", "卢旺达语", "Kinyarwanda"),
    ROMANIAN("ro", "罗马尼亚语", "Romanian"),
    ROMANSH("rm", "罗曼什语", "Romansh"),
    ROMANY("rom", "罗姆语", "Romany"),
    LOJBAN("jbo", "逻辑语", "Lojban"),
    
    // M
    MALAGASY("mg", "马达加斯加语", "Malagasy"),
    MANX("gv", "马恩语", "Manx"),
    MALTESE("mt", "马耳他语", "Maltese"),
    MARATHI("mr", "马拉地语", "Marathi"),
    MALAYALAM("ml", "马拉雅拉姆语", "Malayalam"),
    MALAY("ms", "马来语", "Malay"),
    MARI_RUSSIA("chm", "马里语（俄罗斯）", "Mari (Russia)"),
    MACEDONIAN("mk", "马其顿语", "Macedonian"),
    MARSHALLESE("mh", "马绍尔语", "Marshallese"),
    KEKCHI("kek", "玛雅语", "Kekchí"),
    MAITHILI("mai", "迈蒂利语", "Maithili"),
    MORISYEN("mfe", "毛里求斯克里奥尔语", "Morisyen"),
    MAORI("mi", "毛利语", "Maori"),
    MONGOLIAN("mn", "蒙古语", "Mongolian"),
    BENGALI("bn", "孟加拉语", "Bengali"),
    BURMESE("my", "缅甸语", "Burmese"),
    HMONG("hmn", "苗语", "Hmong"),
    UMBUNDU("umb", "姆班杜语", "Umbundu"),
    
    // N
    NAVAJO("nv", "纳瓦霍语", "Navajo"),
    AFRIKAANS("af", "南非语", "Afrikaans"),
    NEPALI("ne", "尼泊尔语", "Nepali"),
    NIUEAN("niu", "纽埃语", "Niuean"),
    NORWEGIAN("no", "挪威语", "Norwegian"),
    
    // P
    PAM("pmn", "帕姆语", "Pam"),
    PAPIAMENTO("pap", "帕皮阿门托语", "Papiamento"),
    PANJABI("pa", "旁遮普语", "Panjabi"),
    PORTUGUESE("pt", "葡萄牙语", "Portuguese"),
    PUSHTO("ps", "普什图语", "Pushto"),
    
    // Q
    NYANJA("ny", "齐切瓦语", "Nyanja"),
    TWI("tw", "契维语", "Twi"),
    CHEROKEE("chr", "切罗基语", "Cherokee"),
    
    // R
    JAPANESE("ja", "日语", "Japanese"),
    SWEDISH("sv", "瑞典语", "Swedish"),
    
    // S
    SAMOAN("sm", "萨摩亚语", "Samoan"),
    SANGO("sg", "桑戈语", "Sango"),
    SINHALA("si", "僧伽罗语", "Sinhala"),
    UPPER_SORBIAN("hsb", "上索布语", "Upper Sorbian"),
    ESPERANTO("eo", "世界语", "Esperanto"),
    SLOVENIAN("sl", "斯洛文尼亚语", "Slovenian"),
    SWAHILI("sw", "斯瓦希里语", "Swahili"),
    SOMALI("so", "索马里语", "Somali"),
    SLOVAK("sk", "斯洛伐克语", "Slovak"),
    
    // T
    TAGALOG("tl", "他加禄语", "Tagalog"),
    TAJIK("tg", "塔吉克语", "Tajik"),
    TAHITIAN("ty", "塔希提语", "Tahitian"),
    TELUGU("te", "泰卢固语", "Telugu"),
    TAMIL("ta", "泰米尔语", "Tamil"),
    THAI("th", "泰语", "Thai"),
    TONGA_TONGA_ISLANDS("to", "汤加语（汤加群岛）", "Tonga (Tonga Islands)"),
    TONGA_ZAMBIA("toi", "汤加语（赞比亚）", "Tonga (Zambia)"),
    TIGRINYA("ti", "提格雷尼亚语", "Tigrinya"),
    TUVALU("tvl", "图瓦卢语", "Tuvalu"),
    TUVINIAN("tyv", "图瓦语", "Tuvinian"),
    TURKISH("tr", "土耳其语", "Turkish"),
    TURKMEN("tk", "土库曼语", "Turkmen"),
    
    // W
    WALLOON("wa", "瓦隆语", "Walloon"),
    WARAY_PHILIPPINES("war", "瓦瑞语（菲律宾）", "Waray (Philippines)"),
    WELSH("cy", "威尔士语", "Welsh"),
    VENDA("ve", "文达语", "Venda"),
    VOLAPUK("vo", "沃拉普克语", "Volapük"),
    WOLOF("wo", "沃洛夫语", "Wolof"),
    UDMURT("udm", "乌德穆尔特语", "Udmurt"),
    URDU("ur", "乌尔都语", "Urdu"),
    UZBEK("uz", "乌孜别克语", "Uzbek"),
    
    // X
    SPANISH("es", "西班牙语", "Spanish"),
    INTERLINGUE("ie", "西方国际语", "Interlingue"),
    WESTERN_FRISIAN("fy", "西弗里斯兰语", "Western Frisian"),
    SILESIAN("szl", "西里西亚语", "Silesian"),
    HEBREW("he", "希伯来语", "Hebrew"),
    HILIGAYNON("hil", "希利盖农语", "Hiligaynon"),
    HAWAIIAN("haw", "夏威夷语", "Hawaiian"),
    MODERN_GREEK("el", "现代希腊语", "Modern Greek"),
    LINGUA_FRANCA_NOVA("lfn", "新共同语言", "Lingua Franca Nova"),
    SINDHI("sd", "信德语", "Sindhi"),
    HUNGARIAN("hu", "匈牙利语", "Hungarian"),
    SHONA("sn", "修纳语", "Shona"),
    CEBUANO("ceb", "宿务语", "Cebuano"),
    SYRIAC("syr", "叙利亚语", "Syriac"),
    SUNDANESE("su", "巽他语", "Sundanese"),
    
    // Y
    ARMENIAN("hy", "亚美尼亚语", "Armenian"),
    ACHINESE("ace", "亚齐语", "Achinese"),
    IBAN("iba", "伊班语", "Iban"),
    IGBO("ig", "伊博语", "Igbo"),
    IDO("io", "伊多语", "Ido"),
    ILOKO("ilo", "伊洛卡诺语", "Iloko"),
    INUKTITUT("iu", "伊努克提图特语", "Inuktitut"),
    ITALIAN("it", "意大利语", "Italian"),
    YIDDISH("yi", "意第绪语", "Yiddish"),
    INTERLINGUA("ia", "因特语", "Interlingua"),
    HINDI("hi", "印地语", "Hindi"),
    INDONESIA("id", "印度尼西亚语", "Indonesia"),
    INGUSH("inh", "印古什语", "Ingush"),
    ENGLISH("en", "英语", "English"),
    YORUBA("yo", "约鲁巴语", "Yoruba"),
    VIETNAMESE("vi", "越南语", "Vietnamese"),
    
    // Z
    ZAZA("zza", "扎扎其语", "Zaza"),
    JAVANESE("jv", "爪哇语", "Javanese"),
    CHINESE("zh", "中文", "Chinese"),
    TRADITIONAL_CHINESE("zh-tw", "中文繁体", "Traditional Chinese"),
    CANTONESE("yue", "中文粤语", "Cantonese"),
    ZULU("zu", "祖鲁语", "Zulu");

    private final String code;
    private final String chineseName;
    private final String englishName;

    // 静态缓存，用于快速查找
    private static final Map<String, AliTranslateLanguageEnum> CODE_MAP = 
            Arrays.stream(values()).collect(Collectors.toMap(AliTranslateLanguageEnum::getCode, Function.identity()));

    AliTranslateLanguageEnum(String code, String chineseName, String englishName) {
        this.code = code;
        this.chineseName = chineseName;
        this.englishName = englishName;
    }

    /**
     * 获取语言代码
     * @return 语言代码
     */
    public String getCode() {
        return code;
    }

    /**
     * 获取中文名称
     * @return 中文名称
     */
    public String getChineseName() {
        return chineseName;
    }

    /**
     * 获取英文名称
     * @return 英文名称
     */
    public String getEnglishName() {
        return englishName;
    }

    /**
     * 根据语言代码查找对应的枚举
     * 
     * @param code 语言代码
     * @return 对应的枚举，如果不存在则返回null
     */
    public static AliTranslateLanguageEnum getByCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return null;
        }
        return CODE_MAP.get(code.toLowerCase());
    }

    /**
     * 验证语言代码是否支持
     * 
     * @param code 语言代码
     * @return 如果支持返回true，否则返回false
     */
    public static boolean isSupported(String code) {
        return getByCode(code) != null;
    }

    /**
     * 验证两种语言是否支持互译
     * 
     * <p>根据阿里云文档说明：</p>
     * <ul>
     *   <li>除繁体中文、蒙语、粤语外，其他212种语言可支持任意两种语言之间互译</li>
     *   <li>繁体中文、蒙语、粤语仅支持与中文之间的互译</li>
     * </ul>
     * 
     * @param sourceCode 源语言代码
     * @param targetCode 目标语言代码
     * @return 如果支持互译返回true，否则返回false
     */
    public static boolean canTranslate(String sourceCode, String targetCode) {
        AliTranslateLanguageEnum source = getByCode(sourceCode);
        AliTranslateLanguageEnum target = getByCode(targetCode);
        
        if (source == null || target == null) {
            return false;
        }
        
        // 相同语言不需要翻译
        if (source == target) {
            return true;
        }
        
        // 特殊语言的限制
        boolean sourceIsRestricted = isRestrictedLanguage(source);
        boolean targetIsRestricted = isRestrictedLanguage(target);
        
        // 如果源语言或目标语言是受限语言，则必须有一个是中文
        if (sourceIsRestricted || targetIsRestricted) {
            return source == CHINESE || target == CHINESE;
        }
        
        // 其他语言可以任意互译
        return true;
    }

    /**
     * 判断是否为受限语言（仅支持与中文互译的语言）
     * 
     * @param language 语言枚举
     * @return 如果是受限语言返回true，否则返回false
     */
    private static boolean isRestrictedLanguage(AliTranslateLanguageEnum language) {
        return language == TRADITIONAL_CHINESE || 
               language == MONGOLIAN || 
               language == CANTONESE;
    }

    /**
     * 获取所有支持的语言代码列表
     * 
     * @return 语言代码列表
     */
    public static String[] getAllCodes() {
        return Arrays.stream(values())
                .map(AliTranslateLanguageEnum::getCode)
                .toArray(String[]::new);
    }

    /**
     * 获取所有支持的语言名称列表（中文）
     * 
     * @return 中文语言名称列表
     */
    public static String[] getAllChineseNames() {
        return Arrays.stream(values())
                .map(AliTranslateLanguageEnum::getChineseName)
                .toArray(String[]::new);
    }

    /**
     * 获取常用语言代码列表
     * 
     * @return 常用语言代码数组
     */
    public static String[] getCommonCodes() {
        return new String[]{
                "zh", "en", "ja", "ko", "fr", "de", "es", "pt", "ru", "ar",
                "hi", "th", "vi", "id", "ms", "tl", "it", "nl", "sv", "no"
        };
    }

    @Override
    public String toString() {
        return String.format("%s(%s)", chineseName, code);
    }
}
