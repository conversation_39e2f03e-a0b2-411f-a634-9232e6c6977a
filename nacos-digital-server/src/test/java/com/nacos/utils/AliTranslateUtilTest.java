package com.nacos.utils;

import com.nacos.enums.AliTranslateLanguageEnum;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Disabled;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 阿里云机器翻译工具类测试
 * 
 * <AUTHOR>
 * @since 2025-08-05
 */
@SpringBootTest
public class AliTranslateUtilTest {

    /**
     * 测试单文本翻译 - 中文到英文
     * 注意：此测试需要真实的阿里云API密钥，在CI环境中应该禁用
     */
    @Test
    @Disabled("需要真实的阿里云API密钥，仅在本地测试时启用")
    public void testTranslateTextChineseToEnglish() {
        // 测试数据
        String sourceText = "你好，世界！";
        String sourceLanguage = "zh";
        String targetLanguage = "en";
        String scene = "general";

        // 执行翻译
        AliTranslateUtil.TranslationResult result = AliTranslateUtil.translateText(
                sourceText, sourceLanguage, targetLanguage, scene);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getTranslatedText());
        assertFalse(result.getTranslatedText().trim().isEmpty());
        assertTrue(result.getWordCount() > 0);
        
        System.out.println("原文: " + sourceText);
        System.out.println("译文: " + result.getTranslatedText());
        System.out.println("词数: " + result.getWordCount());
        System.out.println("检测语言: " + result.getDetectedLanguage());
    }

    /**
     * 测试单文本翻译 - 英文到中文
     */
    @Test
    @Disabled("需要真实的阿里云API密钥，仅在本地测试时启用")
    public void testTranslateTextEnglishToChinese() {
        // 测试数据
        String sourceText = "Hello, world!";
        String sourceLanguage = "en";
        String targetLanguage = "zh";
        String scene = "general";

        // 执行翻译
        AliTranslateUtil.TranslationResult result = AliTranslateUtil.translateText(
                sourceText, sourceLanguage, targetLanguage, scene);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getTranslatedText());
        assertFalse(result.getTranslatedText().trim().isEmpty());
        
        System.out.println("原文: " + sourceText);
        System.out.println("译文: " + result.getTranslatedText());
    }

    /**
     * 测试相同语言翻译（应该直接返回原文）
     */
    @Test
    public void testTranslateTextSameLanguage() {
        // 测试数据
        String sourceText = "Hello, world!";
        String sourceLanguage = "en";
        String targetLanguage = "en";
        String scene = "general";

        // 执行翻译
        AliTranslateUtil.TranslationResult result = AliTranslateUtil.translateText(
                sourceText, sourceLanguage, targetLanguage, scene);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(sourceText, result.getTranslatedText());
    }

    /**
     * 测试空文本翻译
     */
    @Test
    public void testTranslateTextEmpty() {
        // 测试数据
        String sourceText = "";
        String sourceLanguage = "zh";
        String targetLanguage = "en";
        String scene = "general";

        // 执行翻译
        AliTranslateUtil.TranslationResult result = AliTranslateUtil.translateText(
                sourceText, sourceLanguage, targetLanguage, scene);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("源文本不能为空", result.getErrorMessage());
    }

    /**
     * 测试空语言代码
     */
    @Test
    public void testTranslateTextEmptyLanguage() {
        // 测试数据
        String sourceText = "Hello";
        String sourceLanguage = "";
        String targetLanguage = "zh";
        String scene = "general";

        // 执行翻译
        AliTranslateUtil.TranslationResult result = AliTranslateUtil.translateText(
                sourceText, sourceLanguage, targetLanguage, scene);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("语言代码不能为空", result.getErrorMessage());
    }

    /**
     * 测试超长文本翻译
     */
    @Test
    public void testTranslateTextTooLong() {
        // 构造超长文本（超过5000字符）
        StringBuilder longText = new StringBuilder();
        for (int i = 0; i < 5001; i++) {
            longText.append("a");
        }

        // 执行翻译
        AliTranslateUtil.TranslationResult result = AliTranslateUtil.translateText(
                longText.toString(), "en", "zh", "general");

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertTrue(result.getErrorMessage().contains("文本长度超过"));
    }

    /**
     * 测试批量翻译
     */
    @Test
    @Disabled("需要真实的阿里云API密钥，仅在本地测试时启用")
    public void testTranslateBatch() {
        // 测试数据
        List<String> textList = Arrays.asList(
                "你好",
                "再见",
                "谢谢",
                "不客气"
        );
        String sourceLanguage = "zh";
        String targetLanguage = "en";
        String scene = "general";

        // 执行批量翻译
        AliTranslateUtil.BatchTranslationResult result = AliTranslateUtil.translateBatch(
                textList, sourceLanguage, targetLanguage, scene);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(textList.size(), result.getTranslatedTexts().size());
        assertEquals(textList.size(), result.getSuccessCount());

        // 打印结果
        for (int i = 0; i < textList.size(); i++) {
            System.out.println("原文: " + textList.get(i) + " -> 译文: " + result.getTranslatedTexts().get(i));
        }
    }

    /**
     * 测试批量翻译 - 空列表
     */
    @Test
    public void testTranslateBatchEmpty() {
        // 执行批量翻译
        AliTranslateUtil.BatchTranslationResult result = AliTranslateUtil.translateBatch(
                null, "zh", "en", "general");

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("文本列表不能为空", result.getErrorMessage());
    }

    /**
     * 测试字幕翻译场景
     */
    @Test
    @Disabled("需要真实的阿里云API密钥，仅在本地测试时启用")
    public void testSubtitleTranslation() {
        // 模拟字幕内容
        String subtitleContent = "WEBVTT\n\n" +
                "00:00:01.000 --> 00:00:05.000\n" +
                "大家好，欢迎观看这个视频。\n\n" +
                "00:00:05.000 --> 00:00:10.000\n" +
                "今天我们将学习如何使用机器翻译。\n\n" +
                "00:00:10.000 --> 00:00:15.000\n" +
                "希望这个教程对大家有帮助。";

        // 执行翻译
        AliTranslateUtil.TranslationResult result = AliTranslateUtil.translateText(
                subtitleContent, "zh", "en", "general");

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getTranslatedText());
        
        System.out.println("原字幕:");
        System.out.println(subtitleContent);
        System.out.println("\n翻译后字幕:");
        System.out.println(result.getTranslatedText());
    }

    // ==================== 语言代码验证测试 ====================

    /**
     * 测试语言代码验证 - 有效语言代码
     */
    @Test
    public void testValidLanguageCodes() {
        // 测试常用语言代码
        assertTrue(AliTranslateUtil.isSupportedLanguage("zh"));
        assertTrue(AliTranslateUtil.isSupportedLanguage("en"));
        assertTrue(AliTranslateUtil.isSupportedLanguage("ja"));
        assertTrue(AliTranslateUtil.isSupportedLanguage("ko"));
        assertTrue(AliTranslateUtil.isSupportedLanguage("fr"));
        assertTrue(AliTranslateUtil.isSupportedLanguage("de"));
        assertTrue(AliTranslateUtil.isSupportedLanguage("es"));

        // 测试特殊语言代码
        assertTrue(AliTranslateUtil.isSupportedLanguage("auto"));
        assertTrue(AliTranslateUtil.isSupportedLanguage("zh-tw"));
        assertTrue(AliTranslateUtil.isSupportedLanguage("yue"));
    }

    /**
     * 测试语言代码验证 - 无效语言代码
     */
    @Test
    public void testInvalidLanguageCodes() {
        // 测试无效语言代码
        assertFalse(AliTranslateUtil.isSupportedLanguage("invalid"));
        assertFalse(AliTranslateUtil.isSupportedLanguage("xyz"));
        assertFalse(AliTranslateUtil.isSupportedLanguage(""));
        assertFalse(AliTranslateUtil.isSupportedLanguage(null));
        assertFalse(AliTranslateUtil.isSupportedLanguage("zh-cn")); // 应该是zh
        assertFalse(AliTranslateUtil.isSupportedLanguage("EN")); // 大小写敏感
    }

    /**
     * 测试语言对互译验证 - 支持的语言对
     */
    @Test
    public void testSupportedLanguagePairs() {
        // 常用语言对
        assertTrue(AliTranslateUtil.isSupportedLanguagePair("zh", "en"));
        assertTrue(AliTranslateUtil.isSupportedLanguagePair("en", "zh"));
        assertTrue(AliTranslateUtil.isSupportedLanguagePair("en", "ja"));
        assertTrue(AliTranslateUtil.isSupportedLanguagePair("fr", "de"));

        // 相同语言
        assertTrue(AliTranslateUtil.isSupportedLanguagePair("zh", "zh"));
        assertTrue(AliTranslateUtil.isSupportedLanguagePair("en", "en"));

        // 受限语言与中文的互译
        assertTrue(AliTranslateUtil.isSupportedLanguagePair("zh", "zh-tw"));
        assertTrue(AliTranslateUtil.isSupportedLanguagePair("zh-tw", "zh"));
        assertTrue(AliTranslateUtil.isSupportedLanguagePair("zh", "yue"));
        assertTrue(AliTranslateUtil.isSupportedLanguagePair("yue", "zh"));
        assertTrue(AliTranslateUtil.isSupportedLanguagePair("zh", "mn"));
        assertTrue(AliTranslateUtil.isSupportedLanguagePair("mn", "zh"));
    }

    /**
     * 测试语言对互译验证 - 不支持的语言对
     */
    @Test
    public void testUnsupportedLanguagePairs() {
        // 受限语言之间的互译（不通过中文）
        assertFalse(AliTranslateUtil.isSupportedLanguagePair("zh-tw", "yue"));
        assertFalse(AliTranslateUtil.isSupportedLanguagePair("yue", "zh-tw"));
        assertFalse(AliTranslateUtil.isSupportedLanguagePair("zh-tw", "mn"));
        assertFalse(AliTranslateUtil.isSupportedLanguagePair("mn", "yue"));

        // 受限语言与其他语言的互译
        assertFalse(AliTranslateUtil.isSupportedLanguagePair("zh-tw", "en"));
        assertFalse(AliTranslateUtil.isSupportedLanguagePair("en", "zh-tw"));
        assertFalse(AliTranslateUtil.isSupportedLanguagePair("yue", "ja"));
        assertFalse(AliTranslateUtil.isSupportedLanguagePair("ja", "yue"));

        // 无效语言代码
        assertFalse(AliTranslateUtil.isSupportedLanguagePair("invalid", "en"));
        assertFalse(AliTranslateUtil.isSupportedLanguagePair("zh", "invalid"));
        assertFalse(AliTranslateUtil.isSupportedLanguagePair("invalid1", "invalid2"));
    }

    /**
     * 测试无效语言代码的翻译
     */
    @Test
    public void testTranslateWithInvalidLanguageCode() {
        // 测试无效源语言代码
        AliTranslateUtil.TranslationResult result1 = AliTranslateUtil.translateText(
                "Hello", "invalid", "zh", "general");
        assertNotNull(result1);
        assertFalse(result1.isSuccess());
        assertTrue(result1.getErrorMessage().contains("不支持的源语言代码"));

        // 测试无效目标语言代码
        AliTranslateUtil.TranslationResult result2 = AliTranslateUtil.translateText(
                "Hello", "en", "invalid", "general");
        assertNotNull(result2);
        assertFalse(result2.isSuccess());
        assertTrue(result2.getErrorMessage().contains("不支持的目标语言代码"));
    }

    /**
     * 测试不支持的语言对翻译
     */
    @Test
    public void testTranslateWithUnsupportedLanguagePair() {
        // 测试繁体中文到英文的翻译（不支持）
        AliTranslateUtil.TranslationResult result = AliTranslateUtil.translateText(
                "你好", "zh-tw", "en", "general");
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertTrue(result.getErrorMessage().contains("不支持") && result.getErrorMessage().contains("翻译"));
    }

    /**
     * 测试获取语言名称
     */
    @Test
    public void testGetLanguageName() {
        assertEquals("中文", AliTranslateUtil.getLanguageName("zh"));
        assertEquals("英语", AliTranslateUtil.getLanguageName("en"));
        assertEquals("日语", AliTranslateUtil.getLanguageName("ja"));
        assertEquals("韩语", AliTranslateUtil.getLanguageName("ko"));
        assertEquals("中文繁体", AliTranslateUtil.getLanguageName("zh-tw"));
        assertEquals("中文粤语", AliTranslateUtil.getLanguageName("yue"));
        assertEquals("自动检测", AliTranslateUtil.getLanguageName("auto"));

        // 无效语言代码
        assertNull(AliTranslateUtil.getLanguageName("invalid"));
        assertNull(AliTranslateUtil.getLanguageName(""));
        assertNull(AliTranslateUtil.getLanguageName(null));
    }

    /**
     * 测试获取支持的语言代码列表
     */
    @Test
    public void testGetSupportedLanguageCodes() {
        String[] allCodes = AliTranslateUtil.getSupportedLanguageCodes();
        assertNotNull(allCodes);
        assertTrue(allCodes.length > 200); // 应该有214种语言

        // 检查是否包含常用语言
        List<String> codeList = Arrays.asList(allCodes);
        assertTrue(codeList.contains("zh"));
        assertTrue(codeList.contains("en"));
        assertTrue(codeList.contains("ja"));
        assertTrue(codeList.contains("ko"));
        assertTrue(codeList.contains("auto"));
    }

    /**
     * 测试获取常用语言代码列表
     */
    @Test
    public void testGetCommonLanguageCodes() {
        String[] commonCodes = AliTranslateUtil.getCommonLanguageCodes();
        assertNotNull(commonCodes);
        assertTrue(commonCodes.length >= 10); // 至少包含10种常用语言

        // 检查是否包含最常用的语言
        List<String> codeList = Arrays.asList(commonCodes);
        assertTrue(codeList.contains("zh"));
        assertTrue(codeList.contains("en"));
        assertTrue(codeList.contains("ja"));
        assertTrue(codeList.contains("ko"));
    }

    /**
     * 测试枚举类的基本功能
     */
    @Test
    public void testLanguageEnum() {
        // 测试根据代码查找枚举
        AliTranslateLanguageEnum chinese = AliTranslateLanguageEnum.getByCode("zh");
        assertNotNull(chinese);
        assertEquals("zh", chinese.getCode());
        assertEquals("中文", chinese.getChineseName());
        assertEquals("Chinese", chinese.getEnglishName());

        // 测试toString方法
        assertEquals("中文(zh)", chinese.toString());

        // 测试不存在的代码
        assertNull(AliTranslateLanguageEnum.getByCode("nonexistent"));
        assertNull(AliTranslateLanguageEnum.getByCode(""));
        assertNull(AliTranslateLanguageEnum.getByCode(null));
    }
}
